import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import { ContractFormData } from '@/lib/meta/types';
import { contractDesignSystem } from '@/lib/meta/design/contract-design-system';
import { formatNorwegianContractDate } from '@/lib/utils/formatting';

interface ContractData {
  companyName: string;
  companyOrgNr: string;
  companyAddress: string;
  employeeName: string;
  employeeBirthDate: string;
  employeeAddress: string;
  position: string;
  positionDescription: string;
  startDate: string;
  employmentType: string;
  probationPeriod: string;
  workHours: string;
  breakTime: string;
  hourlyRate: string;
  overtimeRate: string;
  paymentDate: string;
  travelAllowance: string;
  toolAllowance: string;
  vacationDays: string;
  vacationPay: string;
  sickPay: string;
  noticePeriod: string;
  terminationRules: string;
  pensionProvider: string;
  workInsurance: string;
  tariffAgreement: string;
  competenceDevelopment: string;
  legalReference: string;
  contractDate: string;
}

interface ContractPDFProps {
  formData: ContractFormData;
}

// Create semantic styles from the new design system
const styles = StyleSheet.create({
  // Document Foundation
  page: contractDesignSystem.page.document,

  // Contract Header
  header: contractDesignSystem.contractHeader.container,
  companyName: contractDesignSystem.contractHeader.companyName,
  companyInfo: contractDesignSystem.contractHeader.organizationInfo,
  title: contractDesignSystem.contractHeader.contractTitle,

  // Section 1: Party Identity
  partySection: contractDesignSystem.partyIdentity.container,
  partySectionTitle: contractDesignSystem.partyIdentity.sectionTitle,
  partyRow: contractDesignSystem.partyIdentity.twoColumnRow,
  employerColumn: contractDesignSystem.partyIdentity.employerColumn,
  employeeColumn: contractDesignSystem.partyIdentity.employeeColumn,
  partyLabel: contractDesignSystem.partyIdentity.fieldLabel,
  partyLabelBold: {
    ...contractDesignSystem.partyIdentity.fieldLabel,
    fontFamily: 'Helvetica-Bold',
  },
  partyText: contractDesignSystem.partyIdentity.fieldValue,

  // Section 2: Work Location & Tasks
  workSection: contractDesignSystem.workLocationTasks.container,
  workSectionTitle: contractDesignSystem.workLocationTasks.sectionTitle,
  workLabel: contractDesignSystem.workLocationTasks.fieldLabel,
  workLabelBold: {
    ...contractDesignSystem.workLocationTasks.fieldLabel,
    fontFamily: 'Helvetica-Bold',
  },
  workText: contractDesignSystem.workLocationTasks.fieldValue,

  // Section 3: Employment Terms
  employmentSection: contractDesignSystem.employmentTerms.container,
  employmentSectionTitle: contractDesignSystem.employmentTerms.sectionTitle,
  employmentLabel: contractDesignSystem.employmentTerms.inlineLabel,
  employmentLabelBold: {
    ...contractDesignSystem.employmentTerms.inlineLabel,
    fontFamily: 'Helvetica-Bold',
  },
  employmentText: contractDesignSystem.employmentTerms.inlineValue,

  // Section 4: Work Time & Salary
  salarySection: contractDesignSystem.workTimeSalary.container,
  salarySectionTitle: contractDesignSystem.workTimeSalary.sectionTitle,
  salaryLabel: contractDesignSystem.workTimeSalary.inlineLabel,
  salaryLabelBold: {
    ...contractDesignSystem.workTimeSalary.inlineLabel,
    fontFamily: 'Helvetica-Bold',
  },
  salaryText: contractDesignSystem.workTimeSalary.inlineValue,

  // Sections 5-8: Standard Sections
  standardSection: contractDesignSystem.standardSections.container,
  standardSectionTitle: contractDesignSystem.standardSections.sectionTitle,
  standardLabel: contractDesignSystem.standardSections.inlineLabel,
  standardLabelBold: {
    ...contractDesignSystem.standardSections.inlineLabel,
    fontFamily: 'Helvetica-Bold',
  },
  standardText: contractDesignSystem.standardSections.inlineValue,
  keepTogether: contractDesignSystem.standardSections.keepTogether,



  // Contract Signature
  contentWrapper: contractDesignSystem.page.contentWrapper,
  bottomSection: contractDesignSystem.contractSignature.bottomSection,
  signatureLegalText: contractDesignSystem.contractSignature.legalText,
  signatureSection: contractDesignSystem.contractSignature.container,
  signatureBox: contractDesignSystem.contractSignature.signatureBox,
  signatureLine: contractDesignSystem.contractSignature.signatureLine,
  signatureLabel: contractDesignSystem.contractSignature.roleLabel,
  signatureText: contractDesignSystem.contractSignature.nameText,
  signatureDate: contractDesignSystem.contractSignature.dateText,

  // Page Footer
  pageNumber: contractDesignSystem.pageFooter.pageNumber,
});

// Helper function to transform form data to contract data
const transformFormData = (formData: ContractFormData): ContractData => {

  return {
    companyName: formData.companyName || 'Ringerike Landskap AS',
    companyOrgNr: formData.companyOrgNumber || '***********',
    companyAddress: formData.companyAddress || 'Birchs vei 7, 3530 Røyse',
    employeeName: formData.employeeName || '________________________________',
    employeeBirthDate: formatNorwegianContractDate(formData.employeeBirthDate),
    employeeAddress: formData.employeeAddress || '________________________________',
    position: formData.position || '________________________________',
    positionDescription: 'Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten',
    startDate: formatNorwegianContractDate(formData.startDate),
    employmentType: formData.employmentType === 'fast'
      ? 'Fast ansettelse'
      : `Midlertidig ansettelse${formData.temporaryEndDate ? ` til ${formatNorwegianContractDate(formData.temporaryEndDate)}` : ''}${formData.temporaryReason ? `. Begrunnelse: ${formData.temporaryReason}` : ''}`,
    probationPeriod: formData.probationPeriod ? `${formData.probationMonths || 6} måneder med 14 dagers gjensidig oppsigelsesfrist` : 'Ingen prøvetid',
    workHours: `${formData.workingHoursPerWeek || 37.5} timer per uke, normalt ${formData.workingTime || '07:00-15:00'}`,
    breakTime: formData.breakTime || 'Minst 30 min. ubetalt pause ved arbeidsdag >5,5 t',
    hourlyRate: `kr ${formData.hourlyRate || '___'},-`,
    overtimeRate: `${formData.overtimeRate || 40}% av timelønn`,
    paymentDate: `Den ${formData.paymentDay || 5}. hver måned til kontonummer ${formData.accountNumber || '____.____.__.____'}`,
    travelAllowance: formData.travelAllowance || 'Statens gjeldende satser (pt. 3,50 kr/km)',
    toolAllowance: formData.ownTools ? (formData.toolAllowance || 'kr 1,85 per time ved bruk av eget håndverktøy') : 'Ansatt skal bruke eget verktøy',
    vacationDays: '5 uker per år i henhold til ferieloven',
    vacationPay: '12% av feriepengegrunnlaget',
    sickPay: 'Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom',
    noticePeriod: formData.noticePeriod || '1 måned gjensidig etter prøvetid',
    terminationRules: formData.notificationRules || 'Endringer varsles minimum 2 uker i forveien der mulig',
    pensionProvider: `${formData.pensionProvider || 'Storebrand'} (org.nr ${formData.pensionOrgNumber || '958 995 369'})`,
    workInsurance: `${formData.insuranceProvider || 'Gjensidige Forsikring ASA'} (org.nr ${formData.insuranceOrgNumber || '995 568 217'})`,
    tariffAgreement: 'Ingen tariffavtale er gjeldende per dags dato',
    competenceDevelopment: 'Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen',
    legalReference: `Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle juridiske krav per ${new Date().toLocaleDateString('no-NO')}.`,
    contractDate: new Date().toLocaleDateString('no-NO'),
  };
};

const ContractPDF: React.FC<ContractPDFProps> = ({ formData }) => {
  const data = transformFormData(formData);

  return (
    <Document>
      <Page size="A4" style={styles.page} wrap>
        {/* Main Content Wrapper */}
        <View style={styles.contentWrapper}>
          {/* Header */}
          <View style={styles.header}>
          {/* Logo positioned absolutely to not affect centering */}
          <Image src="/logos/base.png" style={{
            position: 'absolute',
            left: 0,
            top: 0,
            width: 50,
            height: 50
          }} />

          {/* Centered Company Info */}
          <View style={{marginBottom: 16}}>
            <Text style={styles.companyName}>{data.companyName}</Text>
            <Text style={styles.companyInfo}>Org.nr: {data.companyOrgNr}</Text>
            <Text style={styles.companyInfo}>{data.companyAddress}</Text>
          </View>

          <Text style={styles.title}>ARBEIDSKONTRAKT</Text>
        </View>

        {/* Section 1: Contract Parties */}
        <View style={[styles.partySection, styles.keepTogether]}>
          <Text style={styles.partySectionTitle}>1. AVTALEPARTER</Text>
          <View style={styles.partyRow}>
            <View style={styles.employerColumn}>
              <Text style={styles.partyLabelBold}>Arbeidsgiver:</Text>
              <Text style={styles.partyText}>{data.companyName}</Text>
              <Text style={styles.partyLabelBold}>Org.nr:</Text>
              <Text style={styles.partyText}>{data.companyOrgNr}</Text>
              <Text style={styles.partyLabelBold}>Adresse:</Text>
              <Text style={styles.partyText}>{data.companyAddress}</Text>
            </View>
            <View style={styles.employeeColumn}>
              <Text style={styles.partyLabelBold}>Arbeidstaker:</Text>
              <Text style={styles.partyText}>{data.employeeName}</Text>
              <Text style={styles.partyLabelBold}>Fødselsdato:</Text>
              <Text style={styles.partyText}>{data.employeeBirthDate}</Text>
              <Text style={styles.partyLabelBold}>Adresse:</Text>
              <Text style={styles.partyText}>{data.employeeAddress}</Text>
            </View>
          </View>
        </View>

        {/* Section 2: Work Location and Tasks */}
        <View style={[styles.workSection, styles.keepTogether]}>
          <Text style={styles.workSectionTitle}>2. ARBEIDSSTED OG ARBEIDSOPPGAVER</Text>
          <Text style={styles.workLabelBold}>Arbeidssted:</Text>
          <Text style={styles.workText}>Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt</Text>
          <Text style={styles.workLabelBold}>Stillingsbetegnelse:</Text>
          <Text style={styles.workText}>{data.position}</Text>
          <Text style={styles.workLabelBold}>Arbeidsoppgaver:</Text>
          <Text style={styles.workText}>{data.positionDescription}</Text>
        </View>

        {/* Section 3: Employment Terms */}
        <View style={[styles.employmentSection, styles.keepTogether]}>
          <Text style={styles.employmentSectionTitle}>3. ANSETTELSESFORHOLD</Text>
          <Text style={styles.employmentText}><Text style={styles.employmentLabelBold}>Tiltredelsesdato:</Text> {data.startDate}</Text>
          <Text style={styles.employmentText}><Text style={styles.employmentLabelBold}>Ansettelsestype:</Text> {data.employmentType}</Text>
          <Text style={styles.employmentText}><Text style={styles.employmentLabelBold}>Prøvetid:</Text> {data.probationPeriod}</Text>
        </View>

        {/* Section 4: Work Time and Salary */}
        <View style={[styles.salarySection, styles.keepTogether]}>
          <Text style={styles.salarySectionTitle}>4. ARBEIDSTID OG LØNN</Text>
          <Text style={styles.salaryText}><Text style={styles.salaryLabelBold}>Arbeidstid:</Text> {data.workHours}</Text>
          <Text style={styles.salaryText}><Text style={styles.salaryLabelBold}>Pauser:</Text> {data.breakTime}</Text>
          <Text style={styles.salaryText}><Text style={styles.salaryLabelBold}>Timelønn:</Text> {data.hourlyRate}</Text>
          <Text style={styles.salaryText}><Text style={styles.salaryLabelBold}>Overtidstillegg:</Text> {data.overtimeRate}</Text>
          <Text style={styles.salaryText}><Text style={styles.salaryLabelBold}>Utbetaling:</Text> {data.paymentDate}</Text>
          <Text style={styles.salaryText}><Text style={styles.salaryLabelBold}>Kjøregodtgjørelse:</Text> {data.travelAllowance}</Text>
          <Text style={styles.salaryText}><Text style={styles.salaryLabelBold}>Verktøytillegg:</Text> {data.toolAllowance}</Text>
        </View>

        {/* Section 5: Vacation and Leave */}
        <View style={[styles.standardSection, styles.keepTogether]} wrap={false}>
          <Text style={styles.standardSectionTitle}>5. FERIE OG PERMISJON</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Ferie:</Text> {data.vacationDays}</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Feriepenger:</Text> {data.vacationPay}</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Sykepenger:</Text> {data.sickPay}</Text>
        </View>

        {/* Section 6: Notice and Termination */}
        <View style={[styles.standardSection, styles.keepTogether]} wrap={false}>
          <Text style={styles.standardSectionTitle}>6. OPPSIGELSE OG ENDRINGER</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Oppsigelsesfrister:</Text> {data.noticePeriod}</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Varslingsregler:</Text> {data.terminationRules}</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Formkrav:</Text> Oppsigelse skal være skriftlig</Text>
        </View>

        {/* Section 7: Pension and Insurance */}
        <View style={[styles.standardSection, styles.keepTogether]} wrap={false}>
          <Text style={styles.standardSectionTitle}>7. PENSJON OG FORSIKRING</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Pensjon:</Text> {data.pensionProvider}</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Yrkesskadeforsikring:</Text> {data.workInsurance}</Text>
        </View>

        {/* Section 8: Other Terms */}
        <View style={[styles.standardSection, styles.keepTogether]} wrap={false}>
          <Text style={styles.standardSectionTitle}>8. ØVRIGE BESTEMMELSER</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Tariffavtale:</Text> {data.tariffAgreement}</Text>
          <Text style={styles.standardText}><Text style={styles.standardLabelBold}>Kompetanseutvikling:</Text> {data.competenceDevelopment}</Text>
        </View>

        </View>

        {/* Bottom Section with Signature - Auto-aligns to bottom */}
        <View style={styles.bottomSection}>
          {/* Signature Section */}
          <View style={styles.signatureSection}>
            <View style={styles.signatureBox}>
              <Text style={styles.signatureDate}>Dato: {data.contractDate}</Text>
              <Text style={styles.signatureLabel}>Arbeidsgiver</Text>
              <Text style={styles.signatureText}>{data.companyName}</Text>
              <View style={styles.signatureLine}></View>
            </View>
            <View style={styles.signatureBox}>
              <Text style={styles.signatureDate}>Dato: {data.contractDate}</Text>
              <Text style={styles.signatureLabel}>Arbeidstaker</Text>
              <Text style={styles.signatureText}>{data.employeeName}</Text>
              <View style={styles.signatureLine}></View>
            </View>
          </View>

          {/* Legal Reference - moved to very bottom */}
          <Text style={styles.signatureLegalText}>{data.legalReference}</Text>
        </View>

        {/* Page Footer with Page Number - Fixed Implementation */}
        <Text
          style={styles.pageNumber}
          render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`}
          fixed
        />

      </Page>
    </Document>
  );
};

export default ContractPDF;
