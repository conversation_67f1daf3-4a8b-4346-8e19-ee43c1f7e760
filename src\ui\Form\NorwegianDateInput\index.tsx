import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { formatDateDDMMYYYY, parseDDMMYYYY } from '@/lib/utils/formatting';

interface NorwegianDateInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'onChange' | 'value'> {
  label?: string;
  error?: string;
  helper?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  value?: string; // ISO date string (YYYY-MM-DD)
  onChange?: (value: string) => void; // Returns ISO date string
  min?: string; // ISO date string
  max?: string; // ISO date string
}

const NorwegianDateInput = React.forwardRef<HTMLInputElement, NorwegianDateInputProps>(
  ({ label, error, helper, leftIcon, rightIcon, className, value, onChange, min, max, ...props }, ref) => {
    const [displayValue, setDisplayValue] = useState('');
    const [isEditing, setIsEditing] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null);
    
    // Ensure we have an id for accessibility
    const id = props.id || props.name || `norwegian-date-${Math.random().toString(36).substr(2, 9)}`;

    // Convert ISO date to DD.MM.YYYY for display
    useEffect(() => {
      if (value && !isEditing) {
        setDisplayValue(formatDateDDMMYYYY(value));
      }
    }, [value, isEditing]);

    const handleFocus = () => {
      setIsEditing(true);
    };

    const handleBlur = () => {
      setIsEditing(false);
      // Validate and convert DD.MM.YYYY back to ISO format
      const isoDate = parseDDMMYYYY(displayValue);
      if (isoDate && isoDate !== value) {
        onChange?.(isoDate);
      } else if (!isoDate && displayValue) {
        // Invalid format, revert to previous value
        setDisplayValue(value ? formatDateDDMMYYYY(value) : '');
      }
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setDisplayValue(newValue);
      
      // Try to parse and emit ISO date immediately if valid
      const isoDate = parseDDMMYYYY(newValue);
      if (isoDate) {
        onChange?.(isoDate);
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Allow navigation and editing keys
      if (['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
        return;
      }
      
      // Allow numbers and dots
      if (/[0-9.]/.test(e.key)) {
        return;
      }
      
      // Prevent other characters
      e.preventDefault();
    };

    // Merge refs
    const mergedRef = (node: HTMLInputElement) => {
      inputRef.current = node;
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    };

    return (
      <div className="space-y-1">
        {label && (
          <label htmlFor={id} className="block text-sm font-medium text-gray-700">
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
              {leftIcon}
            </div>
          )}
          <input
            ref={mergedRef}
            id={id}
            type="text"
            value={displayValue}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder="dd.mm.yyyy"
            className={cn(
              'w-full rounded-md shadow-sm transition-colors duration-200',
              'border-gray-300 focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50',
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              error && 'border-red-300 focus:border-red-500 focus:ring-red-500',
              className
            )}
            {...props}
          />
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-gray-400">
              {rightIcon}
            </div>
          )}
        </div>
        {(error || helper) && (
          <p className={cn(
            'mt-1 text-sm',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helper}
          </p>
        )}
      </div>
    );
  }
);

NorwegianDateInput.displayName = 'NorwegianDateInput';

export { NorwegianDateInput };
