/**
 * Formatting utilities for dates, strings, and phone numbers
 */

/**
 * Formats a date into a localized string (Norwegian format)
 */
export const formatDate = (date: string | Date): string => {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return new Intl.DateTimeFormat("no-NO", {
        month: "long",
        year: "numeric",
    }).format(dateObj);
};


/**
 * Formats a phone number into a readable format (XXX XX XXX)
 */
export const formatPhoneNumber = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length === 8) {
        return cleaned.replace(/(\d{3})(\d{2})(\d{3})/, "$1 $2 $3");
    }
    return phone;
};

/**
 * Formats a date for Norwegian contracts (dd.mm.yyyy)
 */
export const formatNorwegianContractDate = (dateString: string): string => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
};

/**
 * Formats a date to DD.MM.YYYY format regardless of user locale
 * This ensures consistent display across all browser locales
 */
export const formatDateDDMMYYYY = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);

    // Manual formatting to ensure DD.MM.YYYY regardless of locale
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}.${month}.${year}`;
};

/**
 * Parses DD.MM.YYYY format back to ISO date string (YYYY-MM-DD)
 */
export const parseDDMMYYYY = (dateString: string): string => {
    if (!dateString) return '';

    const parts = dateString.split('.');
    if (parts.length !== 3) return '';

    const [day, month, year] = parts;
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

    // Validate the date
    if (isNaN(date.getTime())) return '';

    return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
};

/**
 * Formats a date with time in Norwegian format (yyyy.MM.dd, Kl.HH:mm)
 */
export const formatNorwegianDateTime = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}.${month}.${day}, Kl.${hours}:${minutes}`;
};
